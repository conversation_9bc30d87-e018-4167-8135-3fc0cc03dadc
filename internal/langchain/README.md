# LangChain Integration Package

本包提供完整的 LangChain-Go 整合功能，為 GoAssistant 項目提供先進的 AI 代理、鏈式處理、記憶管理和 RAG (Retrieval-Augmented Generation) 能力。

## 🎯 核心功能

- **智能代理系統** - 專業化 AI 代理（開發、資料庫、基礎設施、研究）
- **鏈式處理** - 順序、並行、條件式和 RAG 鏈執行
- **記憶管理** - 短期、長期和個人化記憶系統
- **向量儲存** - PostgreSQL + pgvector 的語義搜尋
- **文檔處理** - 多格式文檔載入和分段處理
- **工具整合** - 內部工具與 LangChain 的無縫橋接

## 📁 Package 結構

```
internal/langchain/
├── README.md                 # 本文檔
├── service.go                # 主要服務入口
├── client.go                 # LangChain 客戶端
├── adapter.go                # LangChain 適配器
├── agents/                   # AI 代理系統
├── chains/                   # 鏈式處理
├── memory/                   # 記憶管理
├── vectorstore/              # 向量資料庫
├── documentloader/           # 文檔載入器
└── tools/                    # 工具適配器
```

## 🚀 快速開始

### 1. 初始化 LangChain 服務

```go
package main

import (
    "context"
    "log"
    "log/slog"
    
    "github.com/koopa0/assistant-go/internal/config"
    "github.com/koopa0/assistant-go/internal/langchain"
    "github.com/koopa0/assistant-go/internal/storage/postgres"
)

func main() {
    ctx := context.Background()
    logger := slog.Default()
    
    // 載入配置
    cfg := config.LangChain{
        EnableMemory:    true,
        EnableRAG:      true,
        MaxTokens:      4000,
        Temperature:    0.7,
    }
    
    // 創建資料庫客戶端
    dbClient, err := postgres.NewSQLCClient(dbPool, logger)
    if err != nil {
        log.Fatal(err)
    }
    
    // 初始化 LangChain 服務
    service, err := langchain.NewService(cfg, dbClient, logger)
    if err != nil {
        log.Fatal(err)
    }
    defer service.Close(ctx)
    
    // 服務已準備就緒
    logger.Info("LangChain service initialized successfully")
}
```

### 2. 使用 AI 代理

```go
// 執行開發代理任務
request := &langchain.AgentExecutionRequest{
    UserID: "user123",
    AgentRequest: &agents.AgentRequest{
        Query:    "分析這段 Go 代碼的效能問題",
        Context:  map[string]interface{}{"code": goCode},
        MaxSteps: 5,
    },
}

response, err := service.ExecuteAgent(ctx, agents.AgentTypeDevelopment, request)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("分析結果: %s\n", response.Result)
fmt.Printf("執行步驟: %d\n", len(response.Steps))
```

### 3. 執行 RAG 查詢

```go
// RAG 鏈執行
chainRequest := &langchain.ChainExecutionRequest{
    UserID: "user123",
    ChainRequest: &chains.ChainRequest{
        Input: "什麼是 Go 的 context 包？",
        Parameters: map[string]interface{}{
            "similarity_threshold": 0.8,
            "max_documents": 5,
        },
    },
}

response, err := service.ExecuteChain(ctx, chains.ChainTypeRAG, chainRequest)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("RAG 回答: %s\n", response.Output)
```

### 4. 記憶管理

```go
// 儲存記憶
memoryEntry := &memory.MemoryEntry{
    Type:       memory.MemoryTypeLongTerm,
    UserID:     "user123",
    Content:    "用戶偏好使用 PostgreSQL 資料庫",
    Importance: 0.8,
}

err = service.StoreMemory(ctx, memoryEntry)
if err != nil {
    log.Fatal(err)
}

// 搜尋記憶
query := &memory.MemoryQuery{
    UserID:     "user123",
    Content:    "資料庫偏好",
    Similarity: 0.7,
    Limit:      10,
}

results, err := service.SearchMemory(ctx, query)
if err != nil {
    log.Fatal(err)
}

for _, result := range results {
    fmt.Printf("記憶: %s (相似度: %.3f)\n", 
        result.Entry.Content, result.Similarity)
}
```

## 🔧 配置選項

### LangChain 配置

```go
type LangChain struct {
    // 基本設定
    EnableMemory     bool    `yaml:"enable_memory"`
    EnableRAG        bool    `yaml:"enable_rag"`
    MaxTokens        int     `yaml:"max_tokens"`
    Temperature      float64 `yaml:"temperature"`
    
    // 記憶設定
    MemoryConfig     MemoryConfig     `yaml:"memory"`
    
    // RAG 設定
    RAGConfig        RAGConfig        `yaml:"rag"`
    
    // 代理設定
    AgentConfig      AgentConfig      `yaml:"agents"`
}
```

### 記憶配置

```go
type MemoryConfig struct {
    ShortTermTTL     time.Duration `yaml:"short_term_ttl"`     // 短期記憶存活時間
    LongTermLimit    int           `yaml:"long_term_limit"`    // 長期記憶數量限制
    SimilarityThresh float64       `yaml:"similarity_thresh"`  // 相似度閾值
}
```

### RAG 配置

```go
type RAGConfig struct {
    MaxDocuments        int      `yaml:"max_documents"`         // 最大檢索文檔數
    SimilarityThreshold float64  `yaml:"similarity_threshold"`  // 相似度閾值
    ChunkSize          int      `yaml:"chunk_size"`            // 文檔分段大小
    ChunkOverlap       int      `yaml:"chunk_overlap"`         // 分段重疊
}
```

## 🔍 代理類型

### 1. 開發代理 (Development Agent)
```go
// 代碼分析
agents.AgentTypeDevelopment
```
- **功能**: Go AST 分析、代碼生成、效能分析、重構建議
- **工具**: `godev`, `search`
- **適用**: 代碼審查、效能優化、架構建議

### 2. 資料庫代理 (Database Agent)
```go
// SQL 專家
agents.AgentTypeDatabase
```
- **功能**: SQL 生成、查詢優化、架構探索、效能建議
- **工具**: `postgres`, `search`
- **適用**: 資料庫設計、查詢優化、資料分析

### 3. 基礎設施代理 (Infrastructure Agent)
```go
// DevOps 專家
agents.AgentTypeInfrastructure
```
- **功能**: Kubernetes 管理、Docker 操作、日誌分析、故障排除
- **工具**: `k8s`, `docker`, `search`
- **適用**: 部署管理、監控、故障診斷

### 4. 研究代理 (Research Agent)
```go
// 資訊研究專家
agents.AgentTypeResearch
```
- **功能**: 資訊收集、事實查核、報告生成、來源分析
- **工具**: `search`, `cloudflare`
- **適用**: 市場調研、技術文檔、知識庫建立

## 🔗 鏈式處理類型

### 1. 順序鏈 (Sequential Chain)
```go
chains.ChainTypeSequential
```
依序執行多個步驟，前一步的輸出作為下一步的輸入。

### 2. 並行鏈 (Parallel Chain)
```go
chains.ChainTypeParallel
```
同時執行多個並行任務，最後合併結果。

### 3. 條件鏈 (Conditional Chain)
```go
chains.ChainTypeConditional
```
根據條件動態選擇執行路徑。

### 4. RAG 鏈 (RAG Chain)
```go
chains.ChainTypeRAG
```
結合文檔檢索和生成的強化回答系統。

## 💾 記憶類型

### 1. 短期記憶 (Short-term Memory)
- **用途**: 對話上下文、臨時資料
- **存活**: 會話結束後清除
- **儲存**: 記憶體 + 資料庫緩存

### 2. 長期記憶 (Long-term Memory)
- **用途**: 用戶偏好、學習內容、重要資訊
- **存活**: 永久保存（除非手動刪除）
- **儲存**: PostgreSQL + pgvector 語義搜尋

### 3. 工具記憶 (Tool Memory)
- **用途**: 工具執行歷史、快取結果
- **存活**: 可配置的 TTL
- **儲存**: 資料庫 + 快取層

### 4. 個人化記憶 (Personalization Memory)
- **用途**: 用戶習慣、個人化設定
- **存活**: 永久保存
- **儲存**: 結構化資料 + 語義索引

## 🔧 進階功能

### 1. 自定義代理

```go
// 創建自定義代理
type CustomAgent struct {
    *agents.BaseAgent
    customTool *MyTool
}

func NewCustomAgent(llm llms.Model, config config.LangChain, logger *slog.Logger) *CustomAgent {
    base := agents.NewBaseAgent("custom", llm, config, logger)
    
    agent := &CustomAgent{
        BaseAgent:  base,
        customTool: NewMyTool(),
    }
    
    // 添加自定義能力
    agent.AddCapability(agents.AgentCapability{
        Name:        "custom_analysis",
        Description: "執行自定義分析任務",
        Parameters: map[string]interface{}{
            "input_type": "string",
            "analysis_depth": "string (basic|detailed|comprehensive)",
        },
    })
    
    return agent
}
```

### 2. 自定義記憶提供者

```go
// 實作自定義記憶後端
type CustomMemoryProvider struct {
    // 自定義儲存邏輯
}

func (p *CustomMemoryProvider) Store(ctx context.Context, entry *memory.MemoryEntry) error {
    // 自定義儲存邏輯
    return nil
}

func (p *CustomMemoryProvider) Search(ctx context.Context, query *memory.MemoryQuery) ([]*memory.MemorySearchResult, error) {
    // 自定義搜尋邏輯
    return nil, nil
}
```

### 3. 自定義向量儲存

```go
// 實作自定義向量儲存
type CustomVectorStore struct {
    // 自定義向量儲存邏輯
}

func (vs *CustomVectorStore) AddDocuments(ctx context.Context, docs []schema.Document, options ...vectorstores.Option) ([]string, error) {
    // 自定義文檔添加邏輯
    return nil, nil
}

func (vs *CustomVectorStore) SimilaritySearch(ctx context.Context, query string, numDocuments int, options ...vectorstores.Option) ([]schema.Document, error) {
    // 自定義相似度搜尋邏輯
    return nil, nil
}
```

## 📊 監控和偵錯

### 1. 日誌記錄

```go
// 啟用詳細日誌
logger := slog.New(slog.NewTextHandler(os.Stdout, &slog.HandlerOptions{
    Level: slog.LevelDebug,
}))

service, err := langchain.NewService(cfg, dbClient, logger)
```

### 2. 效能監控

```go
// 獲取服務統計
stats, err := service.GetStats(ctx)
if err != nil {
    log.Fatal(err)
}

fmt.Printf("代理執行次數: %v\n", stats["agent_executions"])
fmt.Printf("記憶使用情況: %v\n", stats["memory_usage"])
fmt.Printf("向量儲存大小: %v\n", stats["vector_store_size"])
```

### 3. 健康檢查

```go
// 檢查服務健康狀態
if err := service.Health(ctx); err != nil {
    log.Printf("服務健康檢查失敗: %v", err)
}
```

## 🐛 故障排除

### 常見問題

1. **記憶搜尋結果為空**
   - 檢查相似度閾值設定
   - 確認 embedding 模型一致性
   - 驗證資料庫連接

2. **RAG 回答品質差**
   - 調整文檔分段大小
   - 增加檢索文檔數量
   - 優化相似度閾值

3. **代理執行超時**
   - 增加 MaxSteps 限制
   - 檢查工具響應時間
   - 優化 LLM 參數

4. **記憶體使用過高**
   - 調整短期記憶 TTL
   - 清理過期記憶項目
   - 優化向量儲存配置

## 📚 相關文檔

- [Agents Package](./agents/README.md) - AI 代理系統詳細說明
- [Chains Package](./chains/README.md) - 鏈式處理功能
- [Memory Package](./memory/README.md) - 記憶管理系統
- [VectorStore Package](./vectorstore/README.md) - 向量儲存和搜尋
- [DocumentLoader Package](./documentloader/README.md) - 文檔處理和載入

## 🤝 貢獻指南

1. 遵循 Go 編碼標準
2. 添加充分的單元測試
3. 更新相關文檔
4. 確保向後相容性
5. 使用語義化版本號

## 📄 許可證

本項目採用 MIT 許可證 - 詳見 [LICENSE](../../LICENSE) 文件。